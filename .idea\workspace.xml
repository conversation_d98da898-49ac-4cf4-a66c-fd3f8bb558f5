<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="395aab24-5e87-4b7e-b554-1946e8e931d3" name="Changes" comment="问数统计demo">
      <change afterPath="$PROJECT_DIR$/epointml-0.2.7-py3-none-any.whl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test/apitest.http" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test/reActtest.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test/talktest.http" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/unit/fjzh-opencc.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/协同/talk.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/协同/test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prompts/MQL.md" beforeDir="false" afterPath="$PROJECT_DIR$/prompts/MQL.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/prompts/MQL.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/steps/tools.py" beforeDir="false" afterPath="$PROJECT_DIR$/steps/tools.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test/getParsedS2SQL.http" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test/statistics_demo.py" beforeDir="false" afterPath="$PROJECT_DIR$/test/statistics_demo.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test/一键填表.py" beforeDir="false" afterPath="$PROJECT_DIR$/test/一键填表.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test/问数MQL生成.py" beforeDir="false" afterPath="$PROJECT_DIR$/test/问数MQL生成.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;hszhi&quot;,
      &quot;fullname&quot;: &quot;胡书智&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;http://192.168.0.200/zwjgb-codelab/blbb_agent.git&quot;,
    &quot;second&quot;: &quot;********-adbd-47e8-9e5f-88fc4f848b1a&quot;
  }
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2qPsmOtDxbDNYA8n598gN1oHjBp" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.apitest | #1.executor": "Run",
    "HTTP Request.apitest | #2.executor": "Run",
    "HTTP Request.apitest | #3.executor": "Run",
    "HTTP Request.apitest | #4.executor": "Run",
    "HTTP Request.apitest | #5.executor": "Run",
    "HTTP Request.apitest | #6.executor": "Run",
    "HTTP Request.apitest | #7.executor": "Run",
    "HTTP Request.talktest | #1.executor": "Run",
    "Python.answer.executor": "Run",
    "Python.ch1 (1).executor": "Run",
    "Python.ch1.executor": "Run",
    "Python.ch2.executor": "Run",
    "Python.ch20.executor": "Run",
    "Python.ch22.executor": "Run",
    "Python.ch3.executor": "Run",
    "Python.ch6.executor": "Debug",
    "Python.ch7.executor": "Run",
    "Python.ch8.executor": "Run",
    "Python.convertjson.executor": "Run",
    "Python.converttomd.executor": "Run",
    "Python.ds-sz.executor": "Run",
    "Python.entitysearch.executor": "Debug",
    "Python.field.executor": "Run",
    "Python.fjzh-opencc.executor": "Run",
    "Python.klsoursetest.executor": "Run",
    "Python.query.executor": "Run",
    "Python.qwen-sz (1).executor": "Run",
    "Python.reActtest.executor": "Run",
    "Python.statistics_demo.executor": "Run",
    "Python.talk.executor": "Run",
    "Python.test.executor": "Run",
    "Python.test11.executor": "Run",
    "Python.test2.executor": "Debug",
    "Python.tools.executor": "Run",
    "Python.一键填表.executor": "Run",
    "Python.意图识别-通义千问.executor": "Run",
    "Python.意图识别-通义千问——md.executor": "Run",
    "Python.问数MQL生成.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "D:/python_IDE/blbb_agent/协同",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\python_IDE\blbb_agent\协同" />
      <recent name="D:\python_IDE\blbb_agent\prompts" />
      <recent name="D:\python_IDE\blbb_agent\steps" />
      <recent name="D:\python_IDE\blbb_agent\testgraph" />
      <recent name="D:\python_IDE\blbb_agent\data" />
    </key>
  </component>
  <component name="RunManager" selected="Python.talk">
    <configuration name="talktest | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/test/talktest.http" requestIdentifier="#1" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="reActtest" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="blbb_agent" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test/reActtest.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="statistics_demo" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="blbb_agent" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test/statistics_demo.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="talk" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="blbb_agent" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/协同" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/协同/talk.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="blbb_agent" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/协同" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/协同/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test2" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="blbb_agent" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/steps" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/steps/test2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="HTTP Request.talktest | #1" />
      <item itemvalue="Python.test2" />
      <item itemvalue="Python.talk" />
      <item itemvalue="Python.test" />
      <item itemvalue="Python.reActtest" />
      <item itemvalue="Python.statistics_demo" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.talk" />
        <item itemvalue="Python.test" />
        <item itemvalue="Python.reActtest" />
        <item itemvalue="HTTP Request.talktest | #1" />
        <item itemvalue="Python.statistics_demo" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-50da183f06c8-2887949eec09-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-233.13135.95" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="395aab24-5e87-4b7e-b554-1946e8e931d3" name="Changes" comment="" />
      <created>1734575703114</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1734575703114</updated>
      <workItem from="1734575705491" duration="9273000" />
      <workItem from="1734616927022" duration="682000" />
      <workItem from="1734779568227" duration="28450000" />
      <workItem from="1735521843408" duration="18868000" />
      <workItem from="1735609504252" duration="631000" />
      <workItem from="1735780593093" duration="12537000" />
      <workItem from="1736152861745" duration="30813000" />
      <workItem from="1736756837840" duration="5062000" />
      <workItem from="1737077128293" duration="16165000" />
      <workItem from="1737188357037" duration="648000" />
      <workItem from="1737251547093" duration="12122000" />
      <workItem from="1737362787014" duration="31095000" />
      <workItem from="1738742643437" duration="2001000" />
      <workItem from="1739166784142" duration="3213000" />
      <workItem from="1739265796108" duration="888000" />
      <workItem from="1739434650307" duration="5874000" />
      <workItem from="1739496760862" duration="332000" />
      <workItem from="1739717283124" duration="628000" />
      <workItem from="1739771918595" duration="5433000" />
      <workItem from="1739993601158" duration="12925000" />
      <workItem from="1740120155340" duration="18805000" />
      <workItem from="1740200996622" duration="5448000" />
      <workItem from="1740239248401" duration="5947000" />
      <workItem from="1740313245105" duration="37487000" />
      <workItem from="1741228794114" duration="1229000" />
      <workItem from="1741364197114" duration="15273000" />
      <workItem from="1741524534370" duration="7128000" />
      <workItem from="1741918257395" duration="2105000" />
      <workItem from="1741934690774" duration="1218000" />
      <workItem from="1743388702298" duration="627000" />
      <workItem from="1744267840826" duration="1193000" />
      <workItem from="1744349801832" duration="1668000" />
      <workItem from="1744361333518" duration="600000" />
      <workItem from="1744699581076" duration="3165000" />
      <workItem from="1744859280353" duration="732000" />
      <workItem from="1745217513115" duration="594000" />
      <workItem from="1745546092776" duration="1204000" />
      <workItem from="1745737000108" duration="4276000" />
      <workItem from="1745911286888" duration="1734000" />
      <workItem from="1747719953967" duration="15724000" />
      <workItem from="1747876374689" duration="8539000" />
      <workItem from="1748481004435" duration="10707000" />
      <workItem from="1748937118503" duration="2137000" />
      <workItem from="1749085065658" duration="7893000" />
      <workItem from="1749195965108" duration="521000" />
      <workItem from="1749433425935" duration="3712000" />
      <workItem from="1749534676140" duration="941000" />
      <workItem from="1749606008155" duration="1872000" />
      <workItem from="1749690661996" duration="593000" />
      <workItem from="1749777356441" duration="11590000" />
      <workItem from="1749815167178" duration="6749000" />
      <workItem from="1750748137530" duration="645000" />
      <workItem from="1750751825269" duration="733000" />
      <workItem from="1751266032479" duration="19098000" />
      <workItem from="1751418264215" duration="3691000" />
      <workItem from="1752024359045" duration="2870000" />
      <workItem from="1752039963017" duration="29000" />
      <workItem from="1752199647346" duration="2153000" />
      <workItem from="1752217112923" duration="3870000" />
      <workItem from="1752564965937" duration="1750000" />
      <workItem from="1753259110842" duration="24879000" />
      <workItem from="1753667890360" duration="18383000" />
      <workItem from="1753943338250" duration="10216000" />
      <workItem from="1754273914876" duration="34994000" />
      <workItem from="1754878971463" duration="12860000" />
      <workItem from="1755220784954" duration="21209000" />
      <workItem from="1755833076745" duration="3246000" />
      <workItem from="1756090366909" duration="1818000" />
      <workItem from="1756100853677" duration="8734000" />
    </task>
    <task id="LOCAL-00001" summary="图谱文件喂给模型问答脚本">
      <option name="closed" value="true" />
      <created>1734588186226</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1734588186226</updated>
    </task>
    <task id="LOCAL-00002" summary="总控提示词">
      <option name="closed" value="true" />
      <created>1735529555212</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1735529555212</updated>
    </task>
    <task id="LOCAL-00003" summary="总控提示词">
      <option name="closed" value="true" />
      <created>1735530144701</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1735530144701</updated>
    </task>
    <task id="LOCAL-00004" summary="意图识别">
      <option name="closed" value="true" />
      <created>1735542250550</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1735542250550</updated>
    </task>
    <task id="LOCAL-00005" summary="意图识别">
      <option name="closed" value="true" />
      <created>1735542265759</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1735542265759</updated>
    </task>
    <task id="LOCAL-00006" summary="意图识别">
      <option name="closed" value="true" />
      <created>1735807227979</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1735807227979</updated>
    </task>
    <task id="LOCAL-00007" summary="安抚寒暄无上下文">
      <option name="closed" value="true" />
      <created>1735894343452</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1735894343452</updated>
    </task>
    <task id="LOCAL-00008" summary="安抚寒暄无上下文">
      <option name="closed" value="true" />
      <created>1735894751774</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1735894751774</updated>
    </task>
    <task id="LOCAL-00009" summary="指南图谱脚本">
      <option name="closed" value="true" />
      <created>1736242797760</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1736242797760</updated>
    </task>
    <task id="LOCAL-00010" summary="问数统计demo">
      <option name="closed" value="true" />
      <created>1752200623065</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752200623065</updated>
    </task>
    <task id="LOCAL-00011" summary="问数统计demo">
      <option name="closed" value="true" />
      <created>1752223187767</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752223187767</updated>
    </task>
    <option name="localTasksCounter" value="12" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="图谱文件喂给模型问答脚本" />
    <MESSAGE value="总控提示词" />
    <MESSAGE value="意图识别" />
    <MESSAGE value="安抚寒暄无上下文" />
    <MESSAGE value="指南图谱脚本" />
    <MESSAGE value="问数统计demo" />
    <option name="LAST_COMMIT_MESSAGE" value="问数统计demo" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/steps/ch6.py</url>
          <line>564</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$USER_HOME$/AppData/Local/Temp/BNZ.67b72c873df30b0/actionstore.py</url>
          <line>1</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/steps/test2.py</url>
          <line>26</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" log-message="true" suspend="NONE" type="python-line">
          <url>file://$PROJECT_DIR$/steps/klsoursetest.py</url>
          <line>7</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/blbb_agent$ds_sz.coverage" NAME="ds-sz Coverage Results" MODIFIED="1740228192353" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$converttomd.coverage" NAME="converttomd Coverage Results" MODIFIED="1736316862326" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/testgraph" />
    <SUITE FILE_PATH="coverage/blbb_agent$test2.coverage" NAME="test2 Coverage Results" MODIFIED="1740722336442" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch8.coverage" NAME="ch8 Coverage Results" MODIFIED="1740125693904" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch1.coverage" NAME="ch1 Coverage Results" MODIFIED="1745912401391" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/intent" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch22.coverage" NAME="ch22 Coverage Results" MODIFIED="1741528862776" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$answer.coverage" NAME="answer Coverage Results" MODIFIED="1737257855604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$entitysearch.coverage" NAME="entitysearch Coverage Results" MODIFIED="1748938147627" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/blbb_agent$statistics_demo.coverage" NAME="statistics_demo Coverage Results" MODIFIED="1754898243712" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch20.coverage" NAME="ch20 Coverage Results" MODIFIED="1741920003804" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$field.coverage" NAME="field Coverage Results" MODIFIED="1747881837239" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/blbb_agent$_md.coverage" NAME="意图识别-通义千问——md Coverage Results" MODIFIED="1736326585325" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$query.coverage" NAME="query Coverage Results" MODIFIED="1737450958887" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/RAGV2" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch1__1_.coverage" NAME="ch1 (1) Coverage Results" MODIFIED="1740161369414" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch3.coverage" NAME="ch3 Coverage Results" MODIFIED="1734779977821" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$convertjson.coverage" NAME="convertjson Coverage Results" MODIFIED="1736154492677" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch6.coverage" NAME="ch6 Coverage Results" MODIFIED="1736385105163" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$fjzh_opencc.coverage" NAME="fjzh-opencc Coverage Results" MODIFIED="1749433706842" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/unit" />
    <SUITE FILE_PATH="coverage/blbb_agent$.coverage" NAME="一键填表 Coverage Results" MODIFIED="1753752642772" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/blbb_agent$talk.coverage" NAME="talk Coverage Results" MODIFIED="1756367125768" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/协同" />
    <SUITE FILE_PATH="coverage/blbb_agent$tools.coverage" NAME="tools Coverage Results" MODIFIED="1744699806773" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$test11.coverage" NAME="test11 Coverage Results" MODIFIED="1752565053317" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/blbb_agent$_.coverage" NAME="意图识别-通义千问 Coverage Results" MODIFIED="1735541214998" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch7.coverage" NAME="ch7 Coverage Results" MODIFIED="1735181560972" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$qwen_sz__1_.coverage" NAME="qwen-sz (1) Coverage Results" MODIFIED="1740457203507" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$klsoursetest.coverage" NAME="klsoursetest Coverage Results" MODIFIED="1749819725112" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$MQL.coverage" NAME="问数MQL生成 Coverage Results" MODIFIED="1751443879810" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/blbb_agent$ch2.coverage" NAME="ch2 Coverage Results" MODIFIED="1735119748007" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/steps" />
    <SUITE FILE_PATH="coverage/blbb_agent$test.coverage" NAME="test Coverage Results" MODIFIED="1756178256662" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/协同" />
    <SUITE FILE_PATH="coverage/blbb_agent$reActtest.coverage" NAME="reActtest Coverage Results" MODIFIED="1755826205276" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
  </component>
</project>